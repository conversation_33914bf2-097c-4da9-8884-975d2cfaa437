{"name": "juris-legal-platform", "version": "1.0.0", "description": "AI-Powered Legal Practice Management SaaS Platform", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "backend:dev": "cd backend && npm run start:dev", "backend:build": "cd backend && npm run build", "backend:start": "cd backend && npm run start:prod", "dev:all": "concurrently \"npm run dev\" \"npm run backend:dev\"", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"next": "^14.0.0", "react": "^18.2.0", "react-dom": "^18.2.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "@headlessui/react": "^1.7.0", "@heroicons/react": "^2.0.0", "clsx": "^2.0.0", "lucide-react": "^0.294.0", "react-hook-form": "^7.47.0", "@hookform/resolvers": "^3.3.0", "zod": "^3.22.0", "axios": "^1.6.0", "@tanstack/react-query": "^5.8.0", "date-fns": "^2.30.0", "react-datepicker": "^4.21.0", "react-dropzone": "^14.2.0", "react-pdf": "^7.5.0", "recharts": "^2.8.0", "react-calendar": "^4.6.0", "react-hot-toast": "^2.4.0", "@clerk/nextjs": "^4.27.0", "@clerk/themes": "^1.7.0"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "prettier": "^3.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "concurrently": "^8.2.0", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.0", "jest-environment-jsdom": "^29.7.0"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}