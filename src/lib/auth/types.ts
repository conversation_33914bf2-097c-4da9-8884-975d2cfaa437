// Agnostic auth service interface - can be implemented by any auth provider

export interface AuthUser {
  id: string;
  email: string;
  firstName?: string;
  lastName?: string;
  imageUrl?: string;
  emailVerified: boolean;
  createdAt: Date;
  lastSignInAt?: Date;
  // Custom metadata for our app
  metadata?: {
    firmId?: string;
    role?: string;
    onboardingCompleted?: boolean;
  };
}

export interface AuthSession {
  user: AuthUser;
  token?: string;
  expiresAt?: Date;
}

export interface SignUpData {
  email: string;
  password: string;
  firstName?: string;
  lastName?: string;
  metadata?: Record<string, any>;
}

export interface SignInData {
  email: string;
  password: string;
}

export interface AuthError {
  code: string;
  message: string;
  details?: any;
}

export interface AuthProvider {
  // Core authentication methods
  signUp(data: SignUpData): Promise<AuthUser>;
  signIn(data: SignInData): Promise<AuthSession>;
  signOut(): Promise<void>;
  
  // User management
  getCurrentUser(): Promise<AuthUser | null>;
  updateUser(data: Partial<AuthUser>): Promise<AuthUser>;
  deleteUser(): Promise<void>;
  
  // Session management
  getSession(): Promise<AuthSession | null>;
  refreshSession(): Promise<AuthSession>;
  
  // Organization/Tenant management (for multi-tenancy)
  createOrganization(name: string, metadata?: Record<string, any>): Promise<Organization>;
  getOrganizations(): Promise<Organization[]>;
  switchOrganization(orgId: string): Promise<void>;
  
  // Utility methods
  isAuthenticated(): boolean;
  getToken(): Promise<string | null>;
}

export interface Organization {
  id: string;
  name: string;
  slug: string;
  imageUrl?: string;
  metadata?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export interface AuthConfig {
  provider: 'clerk' | 'auth0' | 'supabase' | 'firebase' | 'custom';
  publishableKey?: string;
  secretKey?: string;
  domain?: string;
  redirectUrl?: string;
  signUpUrl?: string;
  signInUrl?: string;
  afterSignInUrl?: string;
  afterSignUpUrl?: string;
}
