// Core entity types for the Juris platform

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  firmId: string;
  avatar?: string;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export enum UserRole {
  ADMIN = 'ADMIN',
  MANAGING_PARTNER = 'MANAGING_PARTNER',
  PARTNER = 'PARTNER',
  ASSOCIATE = 'ASSOCIATE',
  PARALEGAL = 'PARALEGAL',
  LEGAL_ASSISTANT = 'LEGAL_ASSISTANT',
}

export interface Firm {
  id: string;
  name: string;
  subdomain: string;
  address?: Address;
  phone?: string;
  email?: string;
  website?: string;
  logo?: string;
  subscriptionTier: SubscriptionTier;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum SubscriptionTier {
  SOLO = 'SOLO',
  GROWTH = 'GROWTH',
  ENTERPRISE = 'ENTERPRISE',
}

export interface Address {
  street: string;
  city: string;
  state: string;
  zipCode: string;
  country: string;
}

export interface Case {
  id: string;
  caseNumber: string;
  title: string;
  description?: string;
  practiceArea: PracticeArea;
  status: CaseStatus;
  priority: CasePriority;
  clientId: string;
  assignedLawyerId: string;
  firmId: string;
  openedAt: Date;
  closedAt?: Date;
  nextHearingDate?: Date;
  statute_of_limitations?: Date;
  estimatedValue?: number;
  actualValue?: number;
  customFields?: Record<string, any>;
  createdAt: Date;
  updatedAt: Date;
}

export enum CaseStatus {
  ACTIVE = 'ACTIVE',
  PENDING = 'PENDING',
  ON_HOLD = 'ON_HOLD',
  CLOSED_WON = 'CLOSED_WON',
  CLOSED_LOST = 'CLOSED_LOST',
  CLOSED_SETTLED = 'CLOSED_SETTLED',
  CLOSED_DISMISSED = 'CLOSED_DISMISSED',
}

export enum CasePriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export interface PracticeArea {
  id: string;
  name: string;
  description?: string;
  color: string;
  firmId: string;
  customFields?: CustomField[];
  workflows?: Workflow[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CustomField {
  id: string;
  name: string;
  type: CustomFieldType;
  required: boolean;
  options?: string[]; // For select/multi-select fields
  defaultValue?: any;
}

export enum CustomFieldType {
  TEXT = 'TEXT',
  NUMBER = 'NUMBER',
  DATE = 'DATE',
  BOOLEAN = 'BOOLEAN',
  SELECT = 'SELECT',
  MULTI_SELECT = 'MULTI_SELECT',
  TEXTAREA = 'TEXTAREA',
}

export interface Document {
  id: string;
  filename: string;
  originalName: string;
  mimeType: string;
  size: number;
  s3Key: string;
  s3Bucket: string;
  caseId?: string;
  uploadedById: string;
  firmId: string;
  category?: DocumentCategory;
  tags?: string[];
  version: number;
  parentDocumentId?: string; // For version control
  aiSummary?: string;
  aiExtractedData?: Record<string, any>;
  isProcessed: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum DocumentCategory {
  PLEADING = 'PLEADING',
  MOTION = 'MOTION',
  DISCOVERY = 'DISCOVERY',
  CORRESPONDENCE = 'CORRESPONDENCE',
  CONTRACT = 'CONTRACT',
  EVIDENCE = 'EVIDENCE',
  RESEARCH = 'RESEARCH',
  TEMPLATE = 'TEMPLATE',
  OTHER = 'OTHER',
}

export interface Task {
  id: string;
  title: string;
  description?: string;
  status: TaskStatus;
  priority: TaskPriority;
  dueDate?: Date;
  assignedToId?: string;
  createdById: string;
  caseId?: string;
  firmId: string;
  estimatedHours?: number;
  actualHours?: number;
  tags?: string[];
  dependencies?: string[]; // Task IDs this task depends on
  createdAt: Date;
  updatedAt: Date;
  completedAt?: Date;
}

export enum TaskStatus {
  TODO = 'TODO',
  IN_PROGRESS = 'IN_PROGRESS',
  REVIEW = 'REVIEW',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
}

export enum TaskPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  URGENT = 'URGENT',
}

export interface CalendarEvent {
  id: string;
  title: string;
  description?: string;
  startTime: Date;
  endTime: Date;
  location?: string;
  eventType: EventType;
  caseId?: string;
  attendees?: string[]; // User IDs
  createdById: string;
  firmId: string;
  isAllDay: boolean;
  recurrence?: RecurrenceRule;
  externalEventId?: string; // For Google Calendar/Outlook sync
  createdAt: Date;
  updatedAt: Date;
}

export enum EventType {
  HEARING = 'HEARING',
  DEPOSITION = 'DEPOSITION',
  MEETING = 'MEETING',
  DEADLINE = 'DEADLINE',
  COURT_DATE = 'COURT_DATE',
  CLIENT_MEETING = 'CLIENT_MEETING',
  INTERNAL_MEETING = 'INTERNAL_MEETING',
  OTHER = 'OTHER',
}

export interface RecurrenceRule {
  frequency: 'DAILY' | 'WEEKLY' | 'MONTHLY' | 'YEARLY';
  interval: number;
  endDate?: Date;
  count?: number;
}

export interface Contact {
  id: string;
  firstName: string;
  lastName: string;
  email?: string;
  phone?: string;
  address?: Address;
  company?: string;
  title?: string;
  contactType: ContactType;
  firmId: string;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export enum ContactType {
  CLIENT = 'CLIENT',
  OPPOSING_COUNSEL = 'OPPOSING_COUNSEL',
  JUDGE = 'JUDGE',
  EXPERT_WITNESS = 'EXPERT_WITNESS',
  COURT_REPORTER = 'COURT_REPORTER',
  VENDOR = 'VENDOR',
  OTHER = 'OTHER',
}

export interface Workflow {
  id: string;
  name: string;
  description?: string;
  practiceAreaId: string;
  firmId: string;
  steps: WorkflowStep[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface WorkflowStep {
  id: string;
  name: string;
  description?: string;
  order: number;
  taskTemplates: TaskTemplate[];
  conditions?: WorkflowCondition[];
}

export interface TaskTemplate {
  title: string;
  description?: string;
  priority: TaskPriority;
  estimatedHours?: number;
  daysFromCaseStart?: number;
  daysFromPreviousStep?: number;
  assignToRole?: UserRole;
}

export interface WorkflowCondition {
  field: string;
  operator: 'equals' | 'not_equals' | 'greater_than' | 'less_than' | 'contains';
  value: any;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Form types
export interface LoginForm {
  email: string;
  password: string;
}

export interface RegisterForm {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  firmName: string;
  subdomain: string;
}

export interface CreateCaseForm {
  title: string;
  description?: string;
  practiceAreaId: string;
  clientId: string;
  assignedLawyerId: string;
  priority: CasePriority;
  customFields?: Record<string, any>;
}
